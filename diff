
diff --git a/app/code/Comave/Sales/etc/install-data/new_invoice_guest_comave.html b/app/code/Comave/Sales/etc/install-data/new_invoice_guest_comave.html
new file mode 100644
index 000000000..0fd346025
--- /dev/null
+++ b/app/code/Comave/Sales/etc/install-data/new_invoice_guest_comave.html
@@ -0,0 +1,152 @@
+{{template config_path="design/email/header_template"}}
+<p>
+          {{trans "****This is an automatically generated email, please do not reply****"}}
+ </p>
+
+<table align="center" style="display: block; text-align:center; width: 660px;">
+
+</table>
+<table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px">
+    <tbody>
+
+ <tr>
+<td align="left" style="padding-top: 10px;padding-bottom:10px;">
+                <h2 style="margin: 10px 0 !important; padding-left:20px;">
+                     {{trans "Hello"}} {{trans " %customer_name," customer_name=$myvar3}}
+                </h2>
+ </td>
+
+</tr>
+
+
+    <tr>
+        <td style="margin-left: 20px">
+            <p style="margin: 10px 0 !important; padding-left:20px;padding-right:20px;">
+          {{trans 'Thank you for shopping with ComAve. We have successfully placed your order. You will get a confirmation once your item has been shipped. All your order details are mentioned below. '}} </p>
+         <p style="margin: 10px 0 !important; padding-left:20px;padding-right:20px;">
+ {{trans 'We will keep updating the order status to you on your email. Please register on <a href="%account_url">ComAve.com</a>  and earn many perks such as reward points,et notifications if product on sale, create a wish list, get discounts, etc.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
+ </p>
+
+
+<!--<p style="margin: 10px 0 !important; padding-left:20px;">
+                {{trans 'You can view the entire status of your order by checking <a href="%account_url">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
+            </p>-->
+        </td>
+    </tr>
+
+  <tr style="display:block;">
+        <td class="dark" align="left" style="display:block; ">
+            <h3 style="text-align: left; letter-spacing: 0.025em;padding-left:20px;">
+                {{trans "ORDER NUMBER: %increment_id." increment_id=$order.increment_id |raw}}
+            </h3>
+        </td>
+    </tr>
+ <tr>
+        <td align="left" style="">
+            <h2 style="text-align: left; margin: 0 0 20px 0 !important;padding-left:20px;">
+                {{trans 'Your Invoice: #%invoice_id' invoice_id=$invoice.increment_id |raw}}
+            </h2>
+        </td>
+    </tr>
+    </tbody>
+</table>
+
+<table style="width: 660px">
+    <tr class="email-information">
+        <td>
+
+
+            <table class="order-details" style="border-top: 5px solid #000000">
+                <tr>
+                   <td class="address-details" style="padding-top: 40px !important; padding-left:20px;">
+                        <h3 style="color: #555656;">{{trans "BILLING ADDRESS"}}</h3>
+                        <p style="color: #555656;">{{var formattedBillingAddress|raw}}</p>
+                    </td>
+                    {{depend order_data.is_not_virtual}}
+                    <td class="address-details" style="padding-top: 40px !important">
+                        <h3 style="color: #555656;">{{trans "SHIPPING ADDRESS"}}</h3>
+                        <p style="color: #555656;">{{var formattedShippingAddress|raw}}</p>
+                    </td>
+                    {{/depend}}
+                </tr>
+                <tr>
+                   <td class="method-info wp-method-info" style="padding-bottom: 60px !important;padding-left:20px;">
+                        <h3 style="color: #555656;">{{trans "PAYMENT METHOD"}}</h3>
+                        {{var payment_html|raw}}
+                    </td>
+                    {{depend order_data.is_not_virtual}}
+                    <td class="method-info" style="padding-bottom: 60px !important">
+                        <h3 style="color: #555656;">{{trans "SHIPPING METHOD"}}</h3>
+                        <p style="color: #555656;">{{var order.shipping_description}}</p>
+                        {{if shipping_msg}}
+                        <p style="color: #555656;">{{var shipping_msg}}</p>
+                        {{/if}}
+                    </td>
+                    {{/depend}}
+                </tr>
+            </table>
+           {{depend order_data.email_customer_note}}
+            <table class="message-info">
+                <tr>
+                    <td>
+                        {{var order_data.email_customer_note|escape|nl2br}}
+                    </td>
+                </tr>
+            </table>
+            {{/depend}}
+
+            {{layout handle="weltpixel_sales_email_order_invoice_items" invoice=$invoice order=$order invoice_id=$invoice_id order_id=$order_id area="frontend"}}
+        </td>
+    </tr>
+    <tr>
+        <td colspan="2" align="center">
+            <table style="display: block" class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
+                <tbody style="display:block;">
+                    <tr style="display: block">
+                        <td style="display: block">
+                            <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center" width="100%">
+                                <tr>
+                                    <td align="center" style="padding: 8px 0 !important">
+                                        <a href="{{var frontend_base_url}}/en/profile/orders/{{var order.entity_id}}" target="_blank" style="font-weight: bold">{{trans "VIEW ORDER"}}</a>
+                                    </td>
+                                </tr>
+                            </table>
+                        </td>
+                    </tr>
+                </tbody>
+            </table>
+        </td>
+    </tr>
+ <tr>
+        <td style="margin-left: 0px">
+            <p style="margin: 10px 0 !important; padding-left:20px;">
+                {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}}
+            </p>
+        </td>
+ </tr>
+<tr>
+<td style="margin-left: 0px">
+            <p style="margin: 10px 0 !important; padding-left:20px;">
+                {{trans 'Thank you for shopping with us.'}}
+            </p>
+        </td>
+    </tr>
+<tr>
+<td style="margin-left: 0px">
+  <p style="margin: 10px 0 !important; padding-left:20px;">
+                {{trans 'Regards'}}
+            </p>
+            <p style="margin: 10px 0 !important; padding-left:20px;">
+                {{trans 'ComAve'}}
+            </p>
+        </td>
+    </tr>
+ <tr>
+        <!--<td colspan="2" style="padding-top: 35px;padding-left:20px;">
+            {{block class="Magento\Cms\Block\Block" area="frontend" block_id="weltpixel_custom_block_returns"}}
+        </td>-->
+    </tr>
+
+</table>
+
+{{template config_path="design/email/footer_template"}}
diff --git a/app/code/Comave/Sales/etc/install-data/new_shipment_comave.html b/app/code/Comave/Sales/etc/install-data/new_shipment_comave.html
new file mode 100644
index 000000000..a24bf36ea
--- /dev/null
+++ b/app/code/Comave/Sales/etc/install-data/new_shipment_comave.html
@@ -0,0 +1,131 @@
+{{template config_path="design/email/header_template"}}
+<p>
+    {{trans "****This is an automatically generated email, please do not reply****"}}
+</p>
+<div style="padding: 0 10px 0 10px;">
+
+    <table align="center" style="display: block;  text-align:center; width: 660px;">
+        <tbody style="display: block">
+        <tr>
+            <td align="left" style="padding-top: 10px;padding-bottom:10px;">
+                <p class="greeting"  style="padding-top:10px ;padding-left:20px;">{{trans "Hello %name," name=$order_data.customer_name}}</p>
+            </td>
+        </tr>
+        </tbody>
+    </table>
+    <table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px">
+        <tbody>
+        <tr>
+            <td style="margin-left: 0px">
+                <p style="padding-left:20px;">
+                    {{trans 'Thank you for shopping with us. We would like to confirm that your item has been shipped. Your order and shipping details are mentioned below.'}}
+                </p>
+            </td>
+        </tr>
+        <tr>
+            <td style="margin-left: 0px">
+                <p style="padding-left:20px;">
+                    {{layout handle="sales_email_order_shipment_track" shipment_id=$shipment_id order_id=$order_id}}
+                </p>
+            </td>
+        </tr>
+        <tr>
+            <td style="margin-left: 0px">
+                <p style="padding-left:20px;">
+                    {{trans 'To know more about the order, payment and shipping details, please visit <a href="%account_url">My Orders</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
+                </p>
+            </td>
+        </tr>
+
+        <tr style="display: block">
+            <td class="dark"  style="display: block; ">
+                <h3 style="text-align: left; letter-spacing: 0.025em;padding-left:20px;">
+                    {{trans "ORDER NUMBER: %order_id" order_id=$order.increment_id}}
+                </h3>
+            </td>
+        </tr>
+        <tr>
+            <td align="center" style="">
+                <h2 style="text-align: left; margin: 0 0 20px 0 !important;padding-left:20px;">
+                    {{trans 'ComAve shipment id:  #%shipment_id' shipment_id=$shipment.increment_id}}
+                </h2>
+            </td>
+        </tr>
+        </tbody>
+    </table>
+
+    <table style="width: 660px">
+        <tr class="email-information">
+            <td>
+                <table class="order-details" style="border-top: 5px solid #000000">
+                    <tr>
+                        <td class="address-details" style="padding-top: 30px !important;padding-left:20px;">
+                            <h3 style="color: #555656;">{{trans "BILLING ADDRESS"}}</h3>
+                            <p style="color: #555656;">{{var formattedBillingAddress|raw}}</p>
+                        </td>
+                        {{depend order_data.is_not_virtual}}
+                        <td class="address-details" style="padding-top: 30px !important">
+                            <h3 style="color: #555656;">{{trans "SHIPPING ADDRESS"}}</h3>
+                            <p style="color: #555656;">{{var formattedShippingAddress|raw}}</p>
+                        </td>
+                        {{/depend}}
+                    </tr>
+                    <tr>
+                        <td class="method-info wp-method-info" style="padding-bottom: 40px !important;padding-left:20px;">
+                            <h3 style="color: #555656;">{{trans "PAYMENT METHOD"}}</h3>
+                            {{var payment_html|raw}}
+                        </td>
+                        {{depend order_data.is_not_virtual}}
+                        <td class="method-info" style="padding-bottom: 40px !important">
+                            <h3 style="color: #555656;">{{trans "SHIPPING METHOD"}}</h3>
+                            <p style="color: #555656;">{{var order.shipping_description}}</p>
+                            {{if shipping_msg}}
+                            <p style="color: #555656;">{{var shipping_msg}}</p>
+                            {{/if}}
+                        </td>
+                        {{/depend}}
+                    </tr>
+                </table>
+                {{depend comment}}
+                <table class="message-info">
+                    <tr>
+                        <td>
+                            {{var comment|escape|nl2br}}
+                        </td>
+                    </tr>
+                </table>
+                {{/depend}}
+
+                {{layout handle="weltpixel_sales_email_order_shipment_items" shipment=$shipment order=$order shipment_id=$shipment_id order_id=$order_id}}
+            </td>
+        </tr>
+        <tr>
+            <td colspan="2" align="center">
+                <table style="display: block" class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
+                    <tbody style="display: block">
+                    <tr style="display: block">
+                        <td style="display: block">
+                            <table class="inner-wrapper"  cellspacing="0" cellpadding="0" align="center" width="100%">
+                                <tr>
+                                    <td align="center" style="padding: 8px 0 !important">
+                                        <a href="{{var frontend_base_url}}/en/profile/orders/{{var order.entity_id}}" target="_blank" style="font-weight: bold; border:unset !important;">{{trans "VIEW ORDER"}}</a>
+                                    </td>
+                                </tr>
+                            </table>
+                        </td>
+                    </tr>
+                    </tbody>
+                </table>
+            </td>
+        </tr>
+        <tr>
+            <td style="margin-left: 0px">
+                <p style="margin: 10px 0 !important; padding-left:20px;">
+                    {{trans 'If you need further assistance with you order please contact us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}}
+                </p>
+            </td>
+        </tr>
+    </table>
+</div>
+
+{{template config_path="design/email/footer_template"}}
diff --git a/app/code/Comave/Sales/etc/install-data/new_shipment_guest_comave.html b/app/code/Comave/Sales/etc/install-data/new_shipment_guest_comave.html
new file mode 100644
index 000000000..4acaebdd8
--- /dev/null
+++ b/app/code/Comave/Sales/etc/install-data/new_shipment_guest_comave.html
@@ -0,0 +1,137 @@
+{{template config_path="design/email/header_template"}}
+<div style="padding: 0 10px 0 10px;">
+    <table align="center" style="display: block;  text-align:center; width: 660px;">
+        <tbody style="display: block">
+        <tr>
+            <td align="left" style="padding-top: 10px;padding-bottom:10px;">
+                <p class="greeting"  style="padding-top:20px ;padding-left:20px;">{{trans "Hello  %customer_name," customer_name=$myvar1}}</p>
+            </td>
+        </tr>
+        </tbody>
+    </table>
+    <table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px">
+        <tbody>
+        <tr>
+            <td style="margin-left: 0px">
+                <p style="padding-left:20px;">
+                    {{trans 'Thank You for shopping with us. We would like to confirm that your item has been shipped. Your order details are available on the link below.'}}
+                </p>
+            </td>
+        </tr>
+        <tr>
+            <td style="margin-left: 0px">
+                <p style="padding-left:20px;">
+                    {{trans 'Use this number to track your package: TR897451258'}}
+                </p>
+            </td>
+        </tr>
+        <tr>
+            <td style="margin-left: 0px">
+                <p style="padding-left:20px;">
+                    {{trans 'To know more about the order, payment and shipping details please visit <a href="%account_url">My Order</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
+                </p>
+            </td>
+        </tr>
+
+        <tr style="display: block">
+            <td class="dark"  style="display: block; ">
+                <h3 style="text-align: left; letter-spacing: 0.025em;padding-left:20px;">
+                    {{trans "ORDER NUMBER: %order_id" order_id=$order.increment_id}}
+                </h3>
+            </td>
+        </tr>
+        <tr>
+            <td align="center" style="">
+                <h2 style="text-align: left; margin: 0 0 20px 0 !important;padding-left:20px;">
+                    {{trans 'Your Shipment #%shipment_id' shipment_id=$shipment.increment_id}}
+                </h2>
+            </td>
+        </tr>
+        </tbody>
+    </table>
+
+    <table style="width: 660px">
+        <tr class="email-information">
+            <td>
+                <table class="order-details" style="border-top: 5px solid #000000">
+                    <tr>
+                        <td class="address-details" style="padding-top: 40px !important;padding-left:20px;">
+                            <h3 style="color: #555656;">{{trans "BILLING ADDRESS"}}</h3>
+                            <p style="color: #555656;">{{var formattedBillingAddress|raw}}</p>
+                        </td>
+                        {{depend order_data.is_not_virtual}}
+                        <td class="address-details" style="padding-top: 40px !important">
+                            <h3 style="color: #555656;">{{trans "SHIPPING ADDRESS"}}</h3>
+                            <p style="color: #555656;">{{var formattedShippingAddress|raw}}</p>
+                        </td>
+                        {{/depend}}
+                    </tr>
+                    <tr>
+                        <td class="method-info wp-method-info" style="padding-bottom: 60px !important;padding-left:20px;">
+                            <h3 style="color: #555656;">{{trans "PAYMENT METHOD"}}</h3>
+                            {{var payment_html|raw}}
+                        </td>
+                        {{depend order_data.is_not_virtual}}
+                        <td class="method-info" style="padding-bottom: 60px !important">
+                            <h3 style="color: #555656;">{{trans "SHIPPING METHOD"}}</h3>
+                            <p style="color: #555656;">{{var order.shipping_description}}</p>
+                            {{if shipping_msg}}
+                            <p style="color: #555656;">{{var shipping_msg}}</p>
+                            {{/if}}
+                        </td>
+                        {{/depend}}
+                    </tr>
+                </table>
+                {{depend comment}}
+                <table class="message-info">
+                    <tr>
+                        <td>
+                            {{var comment|escape|nl2br}}
+                        </td>
+                    </tr>
+                </table>
+                {{/depend}}
+
+                {{layout handle="weltpixel_sales_email_order_shipment_items" shipment=$shipment order=$order shipment_id=$shipment_id order_id=$order_id}}
+
+                {{layout handle="sales_email_order_shipment_track" shipment_id=$shipment_id order_id=$order_id}}
+
+
+            </td>
+        </tr>
+        <tr>
+            <td colspan="2" align="center">
+                <table style="display: block" class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
+                    <tbody style="display: block">
+                    <tr style="display: block">
+                        <td style="display: block">
+                            <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center" width="100%">
+                                <tr>
+                                    <td align="center" style="padding: 8px 0 !important">
+                                        <a href="{{var frontend_base_url}}/en/profile/orders/{{var order.entity_id}}" target="_blank" style="font-weight: bold">{{trans "VIEW ORDER"}}</a>
+                                    </td>
+                                </tr>
+                            </table>
+                        </td>
+                    </tr>
+                    </tbody>
+                </table>
+            </td>
+        </tr>
+        <tr>
+            <td style="margin-left: 0px">
+                <p style="margin: 10px 0 !important; padding-left:20px;">
+                    {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}}
+                </p>
+            </td>
+        </tr>
+        <tr>
+            <td style="margin-left: 0px">
+                <p style="margin: 10px 0 !important; padding-left:20px;">
+                    {{trans '*Please do not reply to this email, as it does not accommodate replies.'}}
+                </p>
+            </td>
+        </tr>
+    </table>
+</div>
+{{template config_path="design/email/footer_template"}}
diff --git a/app/code/Comave/Sales/etc/install-data/order_new_comave.html b/app/code/Comave/Sales/etc/install-data/order_new_comave.html
new file mode 100644
index 000000000..dbaf76e01
--- /dev/null
+++ b/app/code/Comave/Sales/etc/install-data/order_new_comave.html
@@ -0,0 +1,146 @@
+{{template config_path="design/email/header_template"}}
+
+<p>
+    {{trans "****This is an automatically generated email, please do not reply****"}}
+</p>
+<div style="padding: 0 10px 0 10px;">
+<table align="center" style="display: block;  text-align:center; width: 660px;border:2px solid;">
+    <tbody style="display: block">
+        <tr style="display: block">
+            <td class="dark"  style="display: block; padding-bottom:8px; padding-top:5px; ">
+                <h3 style="text-align: center; text-transform: uppercase;">
+                    {{trans 'Thank You For Your Purchase'}}
+                </h3>
+            </td>
+        </tr>
+        <tr style="display: block">
+            <td class="dark" align="center" style="display: block; padding-bottom:0px; ">
+                <h1 style="text-align: center; margin: 0 !important">
+                    {{trans 'We just received your order!'}}
+                </h1>
+            </td>
+        </tr>
+        <tr style="display: block">
+            <td class="dark" align="center" style="display: block; padding-bottom:8px; ">
+                <h3 style="text-align: center; letter-spacing: 0.025em;">
+                    {{trans 'ORDER NUMBER: <span class="no-link">%increment_id</span>' increment_id=$order.increment_id |raw}}
+                </h3>
+            </td>
+        </tr>
+    </tbody>
+</table>
+<table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px;margin-left:12px;">
+    <tbody>
+        <tr>
+            <td align="left" style="padding-top: 10px;padding-bottom:10px;">
+                <p class="greeting">{{trans "Hello %customer_name," customer_name=$order_data.customer_name}}</p>
+            </td>
+        </tr>
+        <tr>
+            <td>
+                <p>
+                    {{trans 'Thank you for your purchase. Your order is in process. You will receive the invoice email, and once the order is shipped, we will send you an email with your order tracking number.' }}
+                </p>
+
+                <p style="margin: 10px 0 !important; margin-left:20px;">
+                    {{trans 'If you would like to view the status of your order  <a href="%account_url" style="color: blue !important;">your account</a>'. account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
+                </p>
+
+                <p>
+                    <span style= "color:#d91f26 !important;">Congratulations! </span>{{trans 'You have earned'}} <span style= "color:#d91f26 !important;">"LIX Rewards"</span>{{trans ' on this purchase. You can redeem this rewards on your next purchase, or whenever you feel like it.'}} 
+                </p>
+            </td>
+        </tr>
+    </tbody>
+</table>
+
+<table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px;">
+    <tr class="email-information">
+        <td>
+            <table class="order-details" style="border-top: 5px solid #000000">
+                <tr>
+                    <td class="address-details" style="padding-top: 20px !important; padding-left:20px;">
+                        <h3 style="color: #555656;">{{trans "BILLING ADDRESS"}}</h3>
+                        <p style="color: #555656;">{{var formattedBillingAddress|raw}}</p>
+                    </td>
+                    {{depend order_data.is_not_virtual}}
+                    <td class="address-details" style="padding-top: 20px !important">
+                        <h3 style="color: #555656;">{{trans "SHIPPING ADDRESS"}}</h3>
+                        <p style="color: #555656;">{{var formattedShippingAddress|raw}}</p>
+                    </td>
+                    {{/depend}}
+                </tr>
+                <tr>
+                    <td class="method-info wp-method-info" style="padding-bottom:20px !important;padding-left:20px;">
+                        <h3 style="color: #555656;">{{trans "PAYMENT METHOD"}}</h3>
+                        {{var payment_html|raw}}
+                    </td>
+                    {{depend order_data.is_not_virtual}}
+                    <td class="method-info" style="padding-bottom: 20px !important">
+                        <h3 style="color: #555656;">{{trans "SHIPPING METHOD"}}</h3>
+                        <p style="color: #555656;">{{var order.shipping_description}}</p>
+                        {{if shipping_msg}}
+                        <p style="color: #555656;">{{var shipping_msg}}</p>
+                        {{/if}}
+                    </td>
+                    {{/depend}}
+                </tr>
+            </table>
+            
+            {{depend order_data.email_customer_note}}
+            <table class="message-info">
+                <tr>
+                    <td>
+                        {{var order_data.email_customer_note|escape|nl2br}}
+                    </td>
+                </tr>
+            </table>
+            {{/depend}}
+
+            {{layout handle="weltpixel_sales_email_order_items" order=$order order_id=$order_id area="frontend"}}
+        </td>
+    </tr>
+    
+    <tr>
+        <td colspan="2" align="center">
+            <table style="display: block" class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
+                <tbody style="display: block">
+                    <tr style="display: block">
+                        <td style="display: block">
+                            <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center" width="100%">
+                                <tr>
+                                    <td align="center" style="padding: 8px 0 !important">
+                                        <a href="{{var frontend_base_url}}/en/profile/orders/{{var order.entity_id}}" target="_blank" style="font-weight: bold">{{trans "VIEW ORDER"}}</a>
+                                    </td>
+                                </tr>
+                            </table>
+                        </td>
+                    </tr>
+                </tbody>
+            </table>
+        </td>
+    </tr>
+    
+    <tr>
+        <td style="margin-left: 0px">
+            <p style="margin: 10px 0 !important;padding-left:20px;">
+                {{trans 'If you have questions about your order, please contact us at <a href="mailto:%store_email" style="color: blue !important;">%store_email</a>' store_email=$store_email |raw}}
+            </p>
+        </td>
+    </tr>
+    
+    <tr>
+        <td style="margin-left: 0px">
+            <p style="margin: 10px 0 !important; padding-left:20px;">
+                {{trans 'Thank You For Your Purchase.'}}
+            </p>
+        </td>
+    </tr>
+</table>
+
+<p style="padding-left:28px; padding-top: 10px;">{{trans "Regards,"}}</p>
+<p style="padding-left:28px;color:#d91f26 !important;font-weight: bold !important;">{{trans "ComAve"}}</p>
+
+</div>
+
+{{template config_path="design/email/footer_template"}}
diff --git a/app/code/Comave/Sales/etc/install-data/order_new_guest_comave.html b/app/code/Comave/Sales/etc/install-data/order_new_guest_comave.html
new file mode 100644
index 000000000..f85ed42f2
--- /dev/null
+++ b/app/code/Comave/Sales/etc/install-data/order_new_guest_comave.html
@@ -0,0 +1,138 @@
+{{template config_path="design/email/header_template"}}
+
+
+<table align="center" style="display: block;  text-align:center; width: 660px;border:2px solid;">
+    <tbody style="display: block">
+    <tr style="display: block">
+        <td class="dark"  style="display: block; padding-bottom:8px; padding-top:5px; ">
+           <h3 style="text-align: center; text-transform: uppercase;">
+                  {{trans 'Thank You For Your Purchase'}}
+            </h3>
+        </td>
+    </tr>
+    <tr style="display: block">
+        <td class="dark" align="center" style="display: block; padding-bottom:0px; ">
+            <h1 style="text-align: center; margin: 0 !important">
+                {{trans 'We just received your order!'}}
+            </h1>
+        </td>
+    </tr>
+    <tr style="display: block">
+        <td class="dark" align="center" style="display: block; padding-bottom:8px; ">
+            <h3 style="text-align: center; letter-spacing: 0.025em;">
+                {{trans 'ORDER NUMBER: <span class="no-link">%increment_id</span>' increment_id=$order.increment_id |raw}}
+            </h3>
+        </td>
+    </tr>
+    </tbody>
+</table>
+<div style="padding: 0 10px 0 10px;">
+<table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px;margin-left:12px;">
+    <tbody>
+    <tr>
+            <td align="left" style="padding-top: 10px;padding-bottom:10px;">
+                <h2 style="margin: 10px 0 !important;">
+              <!-- {{trans "Hello, %name" name=$customer.name}} -->
+                     {{trans "Hello"}} {{trans " %customer_name," customer_name=$myvar3}}
+                </h2>
+            </td>
+        </tr>
+   <tr>
+            <td style="margin-left: 20px">
+                <p style="margin: 10px 0 !important; margin-left:20px;">
+                    {{trans 'You can view the entire status of your order by checking <a href="%account_url">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
+                </p>
+            </td>
+        </tr>
+    <tr>
+         <td style="margin-left: 0px">
+                <p style="margin: 10px 0 !important; margin-left:20px;">
+                    {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}}
+                </p>
+            </td>
+    </tr>
+    </tbody>
+</table>
+<table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px;">
+    <tr class="email-information">
+        <td>
+           
+
+            <table class="order-details" style="border-top: 5px solid #000000">
+                <tr>
+                    <td class="address-details" style="padding-top: 20px !important; padding-left:20px;">
+                        <h3 style="color: #555656;">{{trans "BILLING ADDRESS"}}</h3>
+                        <p style="color: #555656;">{{var formattedBillingAddress|raw}}</p>
+                    </td>
+                    {{depend order_data.is_not_virtual}}
+                    <td class="address-details" style="padding-top: 20px !important">
+                        <h3 style="color: #555656;">{{trans "SHIPPING ADDRESS"}}</h3>
+                        <p style="color: #555656;">{{var formattedShippingAddress|raw}}</p>
+                    </td>
+                    {{/depend}}
+                </tr>
+                <tr>
+                  <td class="method-info wp-method-info" style="padding-bottom: 20px !important;padding-left:20px;">
+                        <h3 style="color: #555656;">{{trans "PAYMENT METHOD"}}</h3>
+                        {{var payment_html|raw}}
+                    </td>
+                    {{depend order_data.is_not_virtual}}
+                    <td class="method-info" style="padding-bottom: 20px !important">
+                        <h3 style="color: #555656;">{{trans "SHIPPING METHOD"}}</h3>
+                        <p style="color: #555656;">{{var order.shipping_description}}</p>
+                        {{if shipping_msg}}
+                        <p style="color: #555656;">{{var shipping_msg}}</p>
+                        {{/if}}
+                    </td>
+                    {{/depend}}
+                </tr>
+            </table>
+          {{depend order_data.email_customer_note}}
+            <table class="message-info">
+                <tr>
+                    <td>
+                        {{var order_data.email_customer_note|escape|nl2br}}
+                    </td>
+                </tr>
+            </table>
+            {{/depend}}
+
+            {{layout handle="weltpixel_sales_email_order_items" order=$order order_id=$order_id area="frontend"}}
+        </td>
+    </tr>
+   <tr>
+        <td colspan="2" align="center">
+            <table style="display: block" class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
+                <tbody style="display: block">
+                    <tr style="display: block">
+                        <td style="display: block">
+                            <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center" width="100%">
+                                <tr>
+                                    <td align="center" style="padding: 8px 0 !important">
+                                        <a href="{{var frontend_base_url}}/en/profile/orders/{{var order.entity_id}}" target="_blank" style="font-weight: bold">{{trans "VIEW ORDER"}}</a>
+                                    </td>
+                                </tr>
+                            </table>
+                        </td>
+                    </tr>
+                </tbody>
+            </table>
+        </td>
+    </tr>
+<tr>
+            <td style="margin-left: 0px">
+                <p style="margin: 10px 0 !important; margin-left:20px;">
+                    {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}}
+                </p>
+            </td>
+        </tr>
+    
+    <tr>
+       <!-- <td colspan="2">
+            {{block class="Magento\Cms\Block\Block" area="frontend" block_id="weltpixel_custom_block_returns"}}
+        </td>-->
+    </tr>
+</table>
+</div>
+
+{{template config_path="design/email/footer_template"}}
diff --git a/app/code/Comave/Sales/etc/install-data/order_update_comave.html b/app/code/Comave/Sales/etc/install-data/order_update_comave.html
new file mode 100644
index 000000000..f4b9ce62a
--- /dev/null
+++ b/app/code/Comave/Sales/etc/install-data/order_update_comave.html
@@ -0,0 +1,87 @@
+{{template config_path="design/email/header_template"}}
+
+<table align="center" style="display: block;  text-align:center; width: 660px;border:2px solid;">
+    <tbody style="display: block; ">
+    <tr style="display: block; ">
+        <td class="dark"  style="display: block; padding-bottom:8px; padding-top:5px; ">
+            <h3 style="text-align: center;">
+                {{trans "Hi %name," name=$order_data.customer_name}}
+            </h3>
+        </td>
+    </tr>
+    <tr style="display: block; ">
+        <td class="dark" align="center" style="display: block; padding-bottom:0px; ">
+            <h1 style="text-align: center; margin: 0 !important">
+                {{trans "ORDER %order_status" order_status=$order_data.frontend_status_label}}
+            </h1>
+        </td>
+    </tr>
+    <tr style="display: block; ">
+        <td class="dark" align="center" style="display: block; padding-bottom:8px; ">
+            <h3 style="text-align: center; letter-spacing: 0.025em;">
+                {{trans
+                "ORDER NUMBER: %increment_id " increment_id=$order.increment_id |raw}}
+            </h3>
+        </td>
+    </tr>
+    </tbody>
+</table>
+<table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px">
+    <tbody>
+    <tr>
+        <td align="center" style="padding-top: 10px;padding-bottom:10px;">
+            <h2 style="text-align: center; margin: 0 0 20px 0 !important">
+                {{trans 'Your order has been updated!'}}
+            </h2>
+        </td>
+    </tr>
+    <tr>
+        <td style="margin-left: 0px">
+            <p style="margin: 0 0 50px 0 !important;">
+                {{trans 'You can check the status of your order by logging into <a href="%account_url">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
+            </p>
+        </td>
+    </tr>
+    <tr>
+        <table style="display: block" class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
+            <tbody style="display: block">
+                <tr style="display: block">
+                    <td style="display: block">
+                        <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center" width="100%">
+                            <tr>
+                                <td align="center" style="padding: 8px 0 !important">
+                                    <a href="{{var frontend_base_url}}/en/profile/orders/{{var order.entity_id}}" target="_blank" style="font-weight: bold">{{trans "VIEW ORDER"}}</a>
+                                </td>
+                            </tr>
+                        </table>
+                    </td>
+                </tr>
+            </tbody>
+        </table>
+    </tr>
+    <tr>
+        <td style="margin-left: 0px">
+            <p style="margin: 50px 0 50px 0 !important;">
+                {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}}
+            </p>
+        </td>
+    </tr>
+    </tbody>
+</table>
+<table style="width: 660px">
+    <tr class="email-information">
+        <td>
+            {{depend comment}}
+            <table class="message-info">
+                <tr>
+                    <td>
+                        {{var comment|escape|nl2br}}
+                    </td>
+                </tr>
+            </table>
+            {{/depend}}
+        </td>
+    </tr>
+</table>
+
+{{template config_path="design/email/footer_template"}}
diff --git a/app/code/Comave/Sales/etc/install-data/order_update_guest_comave.html b/app/code/Comave/Sales/etc/install-data/order_update_guest_comave.html
new file mode 100644
index 000000000..19cba40d1
--- /dev/null
+++ b/app/code/Comave/Sales/etc/install-data/order_update_guest_comave.html
@@ -0,0 +1,63 @@
+{{template config_path="design/email/header_template"}}
+
+<table align="center" style="display: block;  text-align:center; width: 660px;border:2px solid;">
+    <tbody style="display: block">
+    <tr style="display: block">
+        <td class="dark"  style="display: block; padding-bottom:8px; padding-top:5px; ">
+            <h3 style="text-align: center;">
+                {{trans "Hi %name," name=$billing.name}}
+            </h3>
+        </td>
+    </tr>
+    <tr style="display: block">
+        <td class="dark" align="center" style="display: block; padding-bottom:0px;">
+            <h1 style="text-align: center; margin: 0 !important">
+                {{trans "ORDER %order_status" order_status=$order_data.frontend_status_label}}
+            </h1>
+        </td>
+    </tr>
+    <tr style="display: block">
+        <td class="dark" align="center" style="display: block; padding-bottom:8px; ">
+            <h3 style="text-align: center; letter-spacing: 0.025em;">
+                {{trans
+                "ORDER NUMBER: %increment_id " increment_id=$order.increment_id |raw}}
+            </h3>
+        </td>
+    </tr>
+    </tbody>
+</table>
+<table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px">
+    <tbody>
+    <tr>
+        <td align="center" style="padding-top: 10px;padding-bottom:10px;">
+            <h2 style="text-align: center; margin: 0 0 20px 0 !important">
+                {{trans 'Your order has been updated.'}}
+            </h2>
+        </td>
+    </tr>
+    <tr>
+        <td style="margin-left: 0px">
+            <p style="">
+                {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}}
+            </p>
+        </td>
+    </tr>
+    </tbody>
+</table>
+<table style="width: 660px">
+    <tr class="email-information">
+        <td>
+            {{depend comment}}
+            <table class="message-info">
+                <tr>
+                    <td>
+                        {{var comment|escape|nl2br}}
+                    </td>
+                </tr>
+            </table>
+            {{/depend}}
+        </td>
+    </tr>
+</table>
+
+{{template config_path="design/email/footer_template"}}
diff --git a/app/code/Comave/Sales/etc/install-data/shipment_update_comave.html b/app/code/Comave/Sales/etc/install-data/shipment_update_comave.html
new file mode 100644
index 000000000..3b8756e65
--- /dev/null
+++ b/app/code/Comave/Sales/etc/install-data/shipment_update_comave.html
@@ -0,0 +1,85 @@
+{{template config_path="design/email/header_template"}}
+<table align="center" style="display: block; text-align:center; width: 660px">
+    <tbody style="display: block">
+    <tr style="display: block">
+        <td class="dark"  style="display:block; padding-bottom:8px; padding-top:5px; ">
+            <h3 style="text-align: center;">
+                {{trans "Hi %name," name=$order_data.customer_name}}
+            </h3>
+        </td>
+    </tr>
+    <tr style="display: block">
+        <td class="dark" align="center" style="display:block; padding-bottom:0px; ">
+            <h1 style="text-align: center; margin: 0 !important">
+                {{trans "ORDER %order_status" order_status=$order_data.frontend_status_label}}
+            </h1>
+        </td>
+    </tr>
+    <tr style="display: block">
+        <td class="dark" align="center" style="display:block; padding-bottom:8px; ">
+            <h3 style="text-align: center; letter-spacing: 0.025em;">
+                {{trans "ORDER NUMBER: %increment_id " increment_id=$order.increment_id |raw}}
+            </h3>
+        </td>
+    </tr>
+    </tbody>
+</table>
+<table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px">
+    <tbody>
+    <tr>
+        <td align="center" style="padding-top: 10px;padding-bottom:10px;">
+            <h2 style="text-align: center; margin: 0 0 20px 0 !important">
+                {{trans 'Your order has been updated!'}}
+            </h2>
+        </td>
+    </tr>
+    <tr>
+        <td style="margin-left: 0px">
+            <p style="margin: 0 0 50px 0 !important;padding-left:20px;">
+                {{trans 'You can check the status of your order by logging into <a href="%account_url">your account</a>.' account_url=$this.getUrl($store,'customer/account/',[_nosid:1]) |raw}}
+            </p>
+        </td>
+    </tr>
+    <tr>
+        <table style="display: block" class="button" width="100%" border="0" cellspacing="0" cellpadding="0">
+            <tbody style="display: block">
+                <tr style="display: block">
+                    <td style="display: block">
+                        <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center" width="100%">
+                            <tr>
+                                <td align="center" style="padding: 8px 0 !important">
+                                    <a href="{{var frontend_base_url}}/en/profile/orders/{{var order.entity_id}}" target="_blank" style="font-weight: bold">{{trans "VIEW ORDER"}}</a>
+                                </td>
+                            </tr>
+                        </table>
+                    </td>
+                </tr>
+            </tbody>
+        </table>
+    </tr>
+    <tr>
+        <td style="margin-left: 0px">
+            <p style="margin-top: 35px !important;padding-left:20px;">
+                {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}}
+            </p>
+        </td>
+    </tr>
+    </tbody>
+</table>
+<table style="width: 660px">
+    <tr class="email-information">
+        <td>
+            {{depend comment}}
+            <table class="message-info">
+                <tr>
+                    <td>
+                        {{var comment|escape|nl2br}}
+                    </td>
+                </tr>
+            </table>
+            {{/depend}}
+        </td>
+    </tr>
+</table>
+
+{{template config_path="design/email/footer_template"}}
diff --git a/app/code/Comave/Sales/etc/install-data/shipment_update_guest_comave.html b/app/code/Comave/Sales/etc/install-data/shipment_update_guest_comave.html
new file mode 100644
index 000000000..e0833fc59
--- /dev/null
+++ b/app/code/Comave/Sales/etc/install-data/shipment_update_guest_comave.html
@@ -0,0 +1,62 @@
+{{template config_path="design/email/header_template"}}
+
+<table align="center" style="display: block; text-align:center; width: 660px">
+    <tbody style="display: block">
+    <tr style="display: block">
+        <td class="dark"  style="display: block; padding-bottom:8px; padding-top:5px; ">
+            <h3 style="text-align: center;">
+                {{trans "Hi %name," name=$billing.name}}
+            </h3>
+        </td>
+    </tr>
+    <tr style="display: block">
+        <td class="dark" align="center" style="display: block; padding-bottom:0px; ">
+            <h1 style="text-align: center; margin: 0 !important">
+                {{trans "ORDER %order_status" order_status=$order_data.frontend_status_label}}
+            </h1>
+        </td>
+    </tr>
+    <tr style="display: block">
+        <td class="dark" align="center" style="display: block; padding-bottom:8px; ">
+            <h3 style="text-align: center; letter-spacing: 0.025em;">
+                {{trans "ORDER NUMBER: %increment_id " increment_id=$order.increment_id |raw}}
+            </h3>
+        </td>
+    </tr>
+    </tbody>
+</table>
+<table align="center" style="padding-bottom:5px; padding-top:20px; width: 660px">
+    <tbody>
+    <tr>
+        <td align="center" style="padding-top: 10px;padding-bottom:10px;">
+            <h2 style="text-align: center; margin: 0 0 20px 0 !important">
+                {{trans 'Your order has been updated!'}}
+            </h2>
+        </td>
+    </tr>
+    <tr>
+        <td style="margin-left: 0px">
+            <p style="padding-left:20px;">
+                {{trans 'If you have questions about your order, you can email us at <a href="mailto:%store_email">%store_email</a>' store_email=$store_email |raw}}
+            </p>
+        </td>
+    </tr>
+    </tbody>
+</table>
+<table style="width: 660px">
+    <tr class="email-information">
+        <td>
+            {{depend comment}}
+            <table class="message-info">
+                <tr>
+                    <td>
+                        {{var comment|escape|nl2br}}
+                    </td>
+                </tr>
+            </table>
+            {{/depend}}
+        </td>
+    </tr>
+</table>
+
+{{template config_path="design/email/footer_template"}}
